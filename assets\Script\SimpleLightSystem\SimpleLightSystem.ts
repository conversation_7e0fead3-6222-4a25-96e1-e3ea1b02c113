// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import SimpleLightSystemManager from "./SimpleLightSystemManager";

const {ccclass, property, executeInEditMode, disallowMultiple} = cc._decorator;

@ccclass
@executeInEditMode
@disallowMultiple
export default class SimpleLightSystem extends cc.Component {
    @property({readonly: true, multiline: false})
    information: string = `光照系统脚本`;

    @property(cc.Material)
    customSpriteMaterial: cc.Material = null;
    

    @property(cc.Color)
    set ambientColor(value: cc.Color) {
        this._ambientColor = value;
        this.SetDirty();
    }
    get ambientColor(): cc.Color {
        return this._ambientColor;
    }
    @property(cc.Color)
    private _ambientColor: cc.Color = cc.Color.WHITE;

    @property({type: cc.Float, range: [0, 10, 0.01]})
    set ambientIntensity(value: number) {
        this._ambientIntensity = value;
        this.SetDirty();
    }
    get ambientIntensity(): number {
        return this._ambientIntensity;
    }
    @property(cc.Float)
    private _ambientIntensity: number = 0.4;

    @property(cc.Sprite)
    testSprite: cc.Sprite = null;


    dataTexture: cc.Texture2D = null;

    onLoad() {
        console.log('### 注册光照系统！')
        SimpleLightSystemManager.instance.systemScript = this;
        this.SetDirty();
    }

    start() {
        this.SetDirty();
        // cc.tween(new TweenObject(0, (val: number)=>{
        //     this.ambientIntensity = val;
        // })).to(0.8, {value: 1}).to(0.8, {value: 0.2}).union().repeatForever().start();
    }

    update(dt: number): void {
        SimpleLightSystemManager.instance.update(dt);
        if(this.testSprite) {
            this.testSprite.spriteFrame = new cc.SpriteFrame(this.dataTexture);
        }
    }

    SetDirty() {
        SimpleLightSystemManager.instance.SetDirty();
    }
}